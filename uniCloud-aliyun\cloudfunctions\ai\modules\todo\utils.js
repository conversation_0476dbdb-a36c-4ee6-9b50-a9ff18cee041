/**
 * 滴答清单 API 工具函数
 * 提供通用的辅助函数和数据处理方法
 * 从 todolist-api 迁移而来，保持完全兼容
 */

const { ERROR_CODES, SUCCESS_RESPONSE, ERROR_RESPONSE } = require('./config.js')

/**
 * 创建成功响应
 * @param {string} message - 成功消息
 * @param {any} data - 响应数据
 * @returns {object} 成功响应对象
 */
function createSuccessResponse(message = '操作成功', data = null) {
  return {
    errCode: null,
    errMsg: message,
    data: data,
  }
}

/**
 * 创建错误响应
 * @param {string} errCode - 错误码
 * @param {string} errMsg - 错误消息
 * @param {any} details - 错误详情
 * @returns {object} 错误响应对象
 */
function createErrorResponse(errCode = ERROR_CODES.UNKNOWN_ERROR, errMsg = '操作失败', details = null) {
  return {
    errCode: errCode,
    errMsg: errMsg,
    details: details,
  }
}

/**
 * 参数校验
 * @param {object} params - 参数对象
 * @param {Array} requiredFields - 必需字段数组
 * @returns {object|null} 如果校验失败返回错误响应，否则返回 null
 */
function validateParams(params, requiredFields) {
  for (const field of requiredFields) {
    if (!params[field] || (typeof params[field] === 'string' && !params[field].trim())) {
      return createErrorResponse(ERROR_CODES.PARAM_IS_NULL, `参数 ${field} 不能为空`)
    }
  }
  return null
}

/**
 * 格式化日期为 API 所需格式
 * @param {string|Date} date - 日期
 * @returns {string} ISO 格式的日期字符串
 */
function formatDateForApi(date) {
  try {
    if (!date) return null

    const dateObj = typeof date === 'string' ? new Date(date) : date
    if (isNaN(dateObj.getTime())) {
      throw new Error('Invalid date')
    }

    return dateObj.toISOString()
  } catch (error) {
    return null
  }
}

/**
 * 格式化日期为可读格式
 * @param {string} isoDate - ISO 格式日期字符串
 * @returns {string} 可读格式的日期字符串
 */
function formatDateForDisplay(isoDate) {
  try {
    if (!isoDate) return null

    const date = new Date(isoDate)
    if (isNaN(date.getTime())) {
      return isoDate
    }

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch (error) {
    return isoDate
  }
}

/**
 * 简化任务数据
 * @param {object} task - 原始任务数据
 * @returns {object} 简化后的任务数据
 */
function simplifyTaskData(task) {
  return {
    id: task.id,
    title: task.title,
    content: task.content || '',
    status: task.status,
    priority: task.priority || 0,
    projectId: task.projectId,
    tags: task.tags || [],
    startDate: task.startDate ? formatDateForDisplay(task.startDate) : null,
    dueDate: task.dueDate ? formatDateForDisplay(task.dueDate) : null,
    completedTime: task.completedTime ? formatDateForDisplay(task.completedTime) : null,
    createdTime: task.createdTime ? formatDateForDisplay(task.createdTime) : null,
    modifiedTime: task.modifiedTime ? formatDateForDisplay(task.modifiedTime) : null,
    isAllDay: task.isAllDay || false,
    reminder: task.reminder,
    kind: task.kind || 'TEXT',
  }
}

/**
 * 简化项目数据
 * @param {object} project - 原始项目数据
 * @returns {object} 简化后的项目数据
 */
function simplifyProjectData(project) {
  return {
    id: project.id,
    name: project.name,
    color: project.color || '#3498db',
    kind: project.kind || 'TASK',
    closed: project.closed || false,
    createdTime: project.createdTime ? formatDateForDisplay(project.createdTime) : null,
    modifiedTime: project.modifiedTime ? formatDateForDisplay(project.modifiedTime) : null,
  }
}

/**
 * 移除对象中的空值字段
 * @param {object} obj - 原始对象
 * @returns {object} 移除空值后的对象
 */
function removeEmptyFields(obj) {
  const result = {}
  for (const [key, value] of Object.entries(obj)) {
    if (value !== null && value !== undefined && value !== '') {
      result[key] = value
    }
  }
  return result
}

/**
 * 解析 HTTP 响应
 * @param {object} response - HTTP 响应对象
 * @returns {object} 解析后的响应数据
 */
function parseHttpResponse(response) {
  try {
    if (!response) {
      return createErrorResponse(ERROR_CODES.PARSE_ERROR, '响应为空')
    }

    // 检查 HTTP 状态码
    if (response.status && response.status !== 200) {
      let errorMsg = `HTTP ${response.status}`
      let errorDetails = response.data
      try {
        // 尝试将 Buffer 转换为字符串
        if (response.data instanceof Buffer) {
          errorDetails = response.data.toString('utf-8')
        }

        // 尝试解析为 JSON
        const errorData = typeof errorDetails === 'string' ? JSON.parse(errorDetails) : errorDetails

        if (errorData && typeof errorData === 'object') {
          errorMsg += `: ${JSON.stringify(errorData)}`
        } else {
          errorMsg += `: ${errorDetails}`
        }
      } catch {
        errorMsg += `: ${errorDetails}`
      }

      return createErrorResponse(ERROR_CODES.API_ERROR, errorMsg, response.data)
    }

    // 解析响应数据
    let responseData
    if (response.data) {
      try {
        let dataToParse = response.data
        // 如果是 Buffer，先转为字符串
        if (dataToParse instanceof Buffer) {
          dataToParse = dataToParse.toString('utf-8')
        }

        responseData = typeof dataToParse === 'string' ? JSON.parse(dataToParse) : dataToParse
      } catch (parseError) {
        return createErrorResponse(ERROR_CODES.PARSE_ERROR, 'JSON 解析失败', response.data)
      }
    } else {
      responseData = {}
    }

    return {
      errCode: null,
      errMsg: '解析成功',
      data: responseData,
      headers: response.headers || {},
    }
  } catch (error) {
    return createErrorResponse(ERROR_CODES.PARSE_ERROR, error.message || '响应解析失败', error)
  }
}

/**
 * 从 cookies 中提取 token
 * @param {object} headers - 响应头对象
 * @returns {string|null} 提取到的 token 或 null
 */
function extractTokenFromCookies(headers) {
  try {
    if (!headers || !headers['set-cookie']) {
      return null
    }

    const cookies = Array.isArray(headers['set-cookie']) ? headers['set-cookie'] : [headers['set-cookie']]

    for (const cookie of cookies) {
      const match = cookie.match(/t=([^;]+)/)
      if (match) {
        return match[1]
      }
    }

    return null
  } catch (error) {
    return null
  }
}

/**
 * 生成随机 ID
 * @returns {string} 随机 ID
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

/**
 * 深度克隆对象
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item))
  }

  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }

  return obj
}

module.exports = {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  formatDateForApi,
  formatDateForDisplay,
  simplifyTaskData,
  simplifyProjectData,
  removeEmptyFields,
  parseHttpResponse,
  extractTokenFromCookies,
  generateId,
  deepClone,
}

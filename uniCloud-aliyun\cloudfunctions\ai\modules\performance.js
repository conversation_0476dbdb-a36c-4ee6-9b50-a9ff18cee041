/**
 * 性能监控模块
 *
 * 主要功能：
 * 1. 负责监控和记录系统性能指标
 * 2. 提供全面的性能数据收集、分析和报告功能
 * 3. 支持实时性能监控和历史数据分析
 * 4. 提供性能优化建议和瓶颈识别
 *
 * 核心特性：
 * - 多维度监控：覆盖计划生成、执行、工具调用等各个环节
 * - 实时统计：实时计算成功率、平均响应时间等关键指标
 * - 历史数据：保存历史性能数据，支持趋势分析
 * - 智能分析：自动识别性能瓶颈和异常情况
 * - 报告生成：生成详细的性能分析报告
 *
 * 监控维度：
 * - 执行计划：计划生成时间、执行时间、步骤耗时
 * - 工具调用：成功率、响应时间、重试率、失败分析
 * - 系统资源：内存使用、CPU 占用、并发处理能力
 * - 用户体验：响应延迟、处理吞吐量、错误率
 *
 * 技术架构：
 * - 轻量级设计：最小化对主业务流程的性能影响
 * - 异步处理：性能数据收集和分析采用异步方式
 * - 内存优化：合理控制历史数据的内存占用
 * - 可扩展性：支持自定义监控指标和分析规则
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

/**
 * 性能监控器
 * 用于收集和分析系统运行时的各项性能指标
 *
 * 核心职责：
 * - 收集执行过程中的各类性能数据
 * - 实时计算和更新性能指标
 * - 提供性能数据的查询和分析接口
 * - 生成性能报告和优化建议
 *
 * 设计原则：
 * - 低侵入性：不影响主业务逻辑的执行
 * - 高精度：提供准确的性能测量和统计
 * - 易扩展：支持新增监控指标和分析维度
 * - 高效率：优化数据结构和算法，减少性能开销
 */
class PerformanceMonitor {
  constructor() {
    // 初始化性能指标数据结构
    // 采用分类管理的方式，便于数据查询和分析
    this.metrics = {
      // 执行计划相关指标
      // 用于监控计划生成和执行的性能表现
      planGenerationTime: [], // 计划生成耗时记录（毫秒）
      planExecutionTime: [], // 计划执行耗时记录（毫秒）
      averageStepTime: [], // 平均步骤耗时记录（毫秒）

      // 工具调用相关指标
      // 用于监控工具调用的成功率和响应性能
      toolCallSuccessRate: 0, // 工具调用成功率（百分比）
      toolCallCount: 0, // 工具调用总次数
      toolCallFailures: 0, // 工具调用失败次数
      averageToolResponseTime: [], // 工具响应时间记录（毫秒）
      retryRate: 0, // 重试率（百分比）

      // 参数解析相关指标
      parameterResolutionTime: [], // 参数解析耗时记录
      dynamicReferenceCount: 0, // 动态引用总数
      resolutionSuccessRate: 0, // 解析成功率

      // 错误统计
      errorsByType: {}, // 按错误类型分类统计
      errorsByTool: {}, // 按工具分类错误统计

      // 用户体验指标
      userSatisfactionScore: 0, // 用户满意度评分
      taskCompletionRate: 0, // 任务完成率
    }
  }

  /**
   * 记录执行计划生成时间
   * @param {number} startTime - 开始时间戳
   * @param {number} endTime - 结束时间戳
   */
  recordPlanGeneration(startTime, endTime) {
    const duration = endTime - startTime
    this.metrics.planGenerationTime.push(duration)

    // 保持最近 100 条记录，避免内存占用过多
    if (this.metrics.planGenerationTime.length > 100) {
      this.metrics.planGenerationTime.shift()
    }
  }

  /**
   * 记录工具调用结果
   * @param {string} toolName - 工具名称
   * @param {boolean} success - 是否成功
   * @param {number} responseTime - 响应时间（毫秒）
   * @param {number} retryCount - 重试次数
   */
  recordToolCall(toolName, success, responseTime, retryCount = 0) {
    this.metrics.toolCallCount++

    if (success) {
      // 记录成功调用的响应时间
      this.metrics.averageToolResponseTime.push(responseTime)
    } else {
      // 记录失败统计
      this.metrics.toolCallFailures++
      this.metrics.errorsByTool[toolName] = (this.metrics.errorsByTool[toolName] || 0) + 1
    }

    // 更新重试率
    if (retryCount > 0) {
      this.metrics.retryRate =
        (this.metrics.retryRate * (this.metrics.toolCallCount - 1) + 1) / this.metrics.toolCallCount
    }

    // 实时更新成功率
    this.metrics.toolCallSuccessRate =
      (this.metrics.toolCallCount - this.metrics.toolCallFailures) / this.metrics.toolCallCount
  }

  // 记录错误
  recordError(errorType, errorMessage, context = {}) {
    this.metrics.errorsByType[errorType] = (this.metrics.errorsByType[errorType] || 0) + 1

    // 记录详细错误信息用于分析
    // 错误信息已记录到 metrics.errorsByType 中
  }

  // 记录参数解析性能
  recordParameterResolution(startTime, endTime, dynamicRefCount, success) {
    const duration = endTime - startTime
    this.metrics.parameterResolutionTime.push(duration)
    this.metrics.dynamicReferenceCount += dynamicRefCount

    // 更新解析成功率
    const totalResolutions = this.metrics.parameterResolutionTime.length
    const currentSuccessRate = this.metrics.resolutionSuccessRate
    this.metrics.resolutionSuccessRate =
      (currentSuccessRate * (totalResolutions - 1) + (success ? 1 : 0)) / totalResolutions
  }

  // 获取性能报告
  getPerformanceReport() {
    return {
      summary: {
        totalToolCalls: this.metrics.toolCallCount,
        successRate: this.metrics.toolCallSuccessRate,
        averageResponseTime: this.calculateAverage(this.metrics.averageToolResponseTime),
        retryRate: this.metrics.retryRate,
      },
      planGeneration: {
        averageTime: this.calculateAverage(this.metrics.planGenerationTime),
        samples: this.metrics.planGenerationTime.length,
      },
      parameterResolution: {
        averageTime: this.calculateAverage(this.metrics.parameterResolutionTime),
        successRate: this.metrics.resolutionSuccessRate,
        totalDynamicReferences: this.metrics.dynamicReferenceCount,
      },
      errors: {
        byType: this.metrics.errorsByType,
        byTool: this.metrics.errorsByTool,
      },
      timestamp: Date.now(),
    }
  }

  calculateAverage(array) {
    if (array.length === 0) return 0
    return array.reduce((sum, val) => sum + val, 0) / array.length
  }

  // 重置指标
  reset() {
    this.metrics = {
      planGenerationTime: [],
      planExecutionTime: [],
      averageStepTime: [],
      toolCallSuccessRate: 0,
      toolCallCount: 0,
      toolCallFailures: 0,
      averageToolResponseTime: [],
      retryRate: 0,
      parameterResolutionTime: [],
      dynamicReferenceCount: 0,
      resolutionSuccessRate: 0,
      errorsByType: {},
      errorsByTool: {},
      userSatisfactionScore: 0,
      taskCompletionRate: 0,
    }
  }
}

/**
 * 全局性能监控实例
 * 整个应用共享的性能监控器，用于收集所有执行过程的性能数据
 * 在 executeRobustPlan 函数中被默认使用
 *
 * 使用方式：
 * - 在执行引擎中自动记录性能数据
 * - 通过 getPerformanceReport() 获取性能报告
 * - 可以通过 reset() 重置统计数据
 */
const globalPerformanceMonitor = new PerformanceMonitor()

module.exports = {
  PerformanceMonitor, // 性能监控器类，可创建新实例
  globalPerformanceMonitor, // 全局共享实例
}

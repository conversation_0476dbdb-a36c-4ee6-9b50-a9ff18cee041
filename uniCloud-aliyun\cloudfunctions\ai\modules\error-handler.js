/**
 * 错误处理模块
 *
 * 主要功能：
 * 1. 负责处理执行过程中的各种错误和异常情况
 * 2. 提供分层的错误处理和恢复策略
 * 3. 支持智能的重试机制和错误分类
 * 4. 提供详细的错误信息和处理状态推送
 *
 * 核心特性：
 * - 分层处理：根据错误类型采用不同的处理策略
 * - 智能重试：基于错误类型和重试次数的智能重试
 * - 错误分类：自动识别参数错误、网络错误、业务错误等
 * - 状态推送：实时推送错误处理状态和结果
 * - 上下文感知：结合执行上下文进行错误分析和处理
 *
 * 错误分类：
 * - 参数验证错误：不可重试，需要修正参数后重新执行
 * - 网络连接错误：可重试，采用指数退避策略
 * - 业务逻辑错误：根据具体情况决定是否重试
 * - 系统资源错误：可重试，但需要延迟重试
 * - 权限认证错误：通常不可重试，需要人工介入
 *
 * 处理策略：
 * - 立即重试：适用于临时性网络错误
 * - 延迟重试：适用于资源繁忙或限流错误
 * - 快速失败：适用于参数错误或权限错误
 * - 错误上报：记录错误信息供后续分析
 *
 * 技术架构：
 * - 静态方法设计：便于全局调用，无需实例化
 * - 异步处理：支持异步的错误处理和重试操作
 * - 状态管理：维护步骤的重试状态和错误信息
 * - 通信机制：通过 SSE 推送错误处理状态
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

/**
 * 增强错误处理器
 * 提供分层的错误处理和恢复机制
 *
 * 核心职责：
 * - 分析和分类不同类型的错误
 * - 根据错误类型选择合适的处理策略
 * - 执行重试、恢复或快速失败操作
 * - 推送错误处理状态和结果信息
 * - 维护错误处理的统计和状态数据
 *
 * 处理流程：
 * 1. 接收错误信息和相关上下文
 * 2. 分析错误类型和可重试性
 * 3. 选择合适的错误处理策略
 * 4. 执行相应的处理操作（重试/失败）
 * 5. 推送处理结果和状态更新
 *
 * 设计原则：
 * - 智能分类：准确识别错误类型和处理策略
 * - 快速响应：及时处理错误，避免长时间等待
 * - 状态透明：提供详细的错误处理状态信息
 * - 可恢复性：优先尝试错误恢复而非直接失败
 */
class EnhancedErrorHandler {
  static async handleToolError(error, step, context, sseChannel) {
    const errorInfo = {
      stepId: step.stepId,
      toolName: step.toolName,
      error: error.message,
      timestamp: Date.now(),
      retryCount: step.retryCount,
    }

    // 1. 参数验证错误（不可重试）
    if (error.message.includes('参数验证失败') || error.message.includes('缺少必需参数')) {
      await sseChannel.write({
        type: 'step_error',
        ...errorInfo,
        errorType: 'validation_error',
        recoverable: false,
        suggestions: this.generateValidationSuggestions(error),
      })
      throw error
    }

    // 2. 工具执行错误（可重试）
    if (step.retryCount < step.maxRetries) {
      await this.retryStep(step, context, sseChannel)
      return 'retry'
    }

    // 3. 不进行降级处理，直接失败

    // 4. 最终失败
    await sseChannel.write({
      type: 'step_error',
      ...errorInfo,
      errorType: 'execution_error',
      recoverable: false,
      suggestions: this.generateErrorSuggestions(error, step),
    })

    throw new Error(`工具执行失败：${error.message}`)
  }

  static async retryStep(step, context, sseChannel) {
    step.retryCount++

    await sseChannel.write({
      type: 'step_retry',
      stepId: step.stepId,
      retryCount: step.retryCount,
      maxRetries: step.maxRetries,
      timestamp: Date.now(),
    })

    // 指数退避重试
    const delay = Math.min(1000 * Math.pow(2, step.retryCount - 1), 10000)
    await new Promise((resolve) => setTimeout(resolve, delay))
  }

  static generateValidationSuggestions(error) {
    const suggestions = []

    if (error.message.includes('缺少必需参数')) {
      suggestions.push('请检查输入是否包含必要的信息')
    }

    if (error.message.includes('格式不符合要求')) {
      suggestions.push('请使用正确的格式输入')
    }

    return suggestions
  }

  static generateErrorSuggestions(error, step) {
    const suggestions = []

    if (error.message.includes('API rate limit')) {
      suggestions.push('请稍后重试，API 调用频率过高')
    }

    if (error.message.includes('unauthorized')) {
      suggestions.push('请检查 API 密钥配置')
    }

    if (step.toolName === 'getProjects' && error.message.includes('not found')) {
      suggestions.push('请确认您有访问项目列表的权限')
    }

    return suggestions
  }
}

module.exports = {
  EnhancedErrorHandler,
}

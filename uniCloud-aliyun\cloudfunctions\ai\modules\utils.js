/**
 * 通用工具函数模块
 * 提供项目中常用的辅助方法和工具函数
 * 包含统一的日志记录系统
 */

/**
 * 生成符合 RFC 4122 标准的 UUID v4
 * @returns {string} 生成的 UUID 字符串
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

/**
 * 从输入文本中提取项目关键词
 * 支持中文"项目"和英文"project"后缀的匹配
 * @param {string} input - 输入文本
 * @returns {string} 提取的项目关键词，未找到时返回空字符串
 */
function extractProjectKeyword(input) {
  const matches = input.match(/(\w+)项目|(\w+)project/gi)
  if (matches && matches.length > 0) {
    return matches[0].replace(/项目|project/gi, '')
  }
  return ''
}

/**
 * 创建延迟执行的 Promise
 * @param {number} ms - 延迟时间（毫秒）
 * @returns {Promise<void>} 延迟 Promise
 */
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/**
 * 深度克隆对象，支持嵌套对象、数组和日期类型
 * @param {*} obj - 要克隆的对象
 * @returns {*} 深度克隆后的对象
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item))
  }

  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }

  return obj
}

/**
 * 将时间戳格式化为 YYYY-MM-DD HH:mm:ss 格式
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
function formatTimestamp(timestamp) {
  const date = new Date(timestamp)
  return date.toISOString().replace('T', ' ').substring(0, 19)
}

/**
 * 计算数值数组的平均值
 * @param {number[]} array - 数值数组
 * @returns {number} 平均值，空数组或非数组时返回 0
 */
function calculateAverage(array) {
  if (!Array.isArray(array) || array.length === 0) {
    return 0
  }
  return array.reduce((sum, val) => sum + val, 0) / array.length
}

/**
 * 安全的 JSON 解析，避免解析异常导致程序崩溃
 * @param {string} jsonString - 要解析的 JSON 字符串
 * @param {*} defaultValue - 解析失败时的默认返回值
 * @returns {*} 解析结果或默认值
 */
function safeJsonParse(jsonString, defaultValue = null) {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    return defaultValue
  }
}

/**
 * 检查值是否为空（null、undefined、空字符串、空数组、空对象）
 * @param {*} obj - 要检查的值
 * @returns {boolean} 是否为空
 */
function isEmpty(obj) {
  if (obj === null || obj === undefined) {
    return true
  }

  if (typeof obj === 'string' || Array.isArray(obj)) {
    return obj.length === 0
  }

  if (typeof obj === 'object') {
    return Object.keys(obj).length === 0
  }

  return false
}

/**
 * 安全地去除字符串首尾空格
 * 处理 null/undefined 情况，避免类型错误
 * @param {*} str - 要处理的字符串
 * @returns {string} 处理后的字符串，非字符串类型返回空字符串
 */
function safeTrim(str) {
  if (typeof str !== 'string') {
    return ''
  }
  return str.trim()
}

/**
 * 计算动态引用数量
 * 统计参数对象中的动态引用数量
 *
 * @param {Object} parameters - 参数对象
 * @returns {number} 动态引用数量
 */
function countDynamicReferences(parameters) {
  if (!parameters || typeof parameters !== 'object') return 0

  let count = 0
  const countInValue = (value) => {
    if (typeof value === 'string') {
      // 统计 $context.、$step.、$filter( 等动态引用
      const matches = value.match(/\$(?:context\.|step\.|filter\()/g)
      if (matches) count += matches.length
    } else if (Array.isArray(value)) {
      value.forEach(countInValue)
    } else if (typeof value === 'object' && value !== null) {
      Object.values(value).forEach(countInValue)
    }
  }

  Object.values(parameters).forEach(countInValue)
  return count
}







module.exports = {
  generateUUID,
  extractProjectKeyword,
  delay,
  deepClone,
  formatTimestamp,
  calculateAverage,
  safeJsonParse,
  isEmpty,
  safeTrim,
  countDynamicReferences,
}

# SSE消息处理修改说明

## 修改目标
修改前端SSE消息处理逻辑，确保前后端显示内容保持一致，避免前端硬编码自定义文本。

## 修改原则

### 1. 职责分离
- **后端负责**：业务逻辑处理 + 用户提示文案
- **前端负责**：UI状态管理 + 消息展示
- **避免重复**：前后端不应该都定义相同的提示文案

### 2. 数据流向
```
后端推送消息 → 前端接收 → 直接显示后端内容
```

### 3. 消息格式统一
所有SSE消息都应该使用标准格式：
```json
{
  "type": "message_type",
  "timestamp": 1704067200000,
  "sessionId": "session_id",
  "data": {
    "message": "后端推送的显示文本",
    // 其他业务数据...
  }
}
```

## 具体修改内容

### 1. 新增消息类型定义
在 `SSE_MESSAGE_TYPES` 中新增了后端实际推送的消息类型：
```javascript
EXECUTION_PLAN: 'execution_plan',
EXECUTION_STEP: 'execution_step', 
STEP_RESULT: 'step_result',
EXECUTION_COMPLETE: 'execution_complete',
```

### 2. 修改消息处理逻辑

#### 修改前（错误做法）：
```javascript
case SSE_MESSAGE_TYPES.PROCESSING_START:
  showLoading('AI 思考中...', 'thinking')  // 前端自定义文本
```

#### 修改后（正确做法）：
```javascript
case SSE_MESSAGE_TYPES.PROCESSING_START:
  showLoading(data.message, 'thinking')  // 使用后端推送的消息
```

### 3. 完整的消息处理映射

| 消息类型 | 修改前 | 修改后 |
|---------|--------|--------|
| `processing_start` | "AI 思考中..." | `data.message` |
| `intent_analyzing` | "分析意图中..." | `data.message` |
| `intent_recognized` | "准备回复中..." / "准备执行任务..." | `data.description` |
| `chat_response_start` | "正在生成回复..." | `data.message` |
| `task_preparation` | "正在准备执行任务..." | `data.message` |
| `execution_step` | 硬编码文本 | `data.step.description` |
| `step_result` | 无处理 | 根据结果状态显示相应信息 |

### 4. 错误处理统一
所有错误消息都使用标准格式：
```javascript
handleStreamMessage({
  type: SSE_MESSAGE_TYPES.ERROR,
  data: { error: "错误信息" }
})
```

## 修改效果

### 1. 前后端一致性
- 后端推送什么消息，前端就显示什么内容
- 避免了前后端文案不一致的问题

### 2. 维护便利性
- 修改提示文案只需要在后端修改一处
- 前端专注于UI逻辑，不需要维护文案内容

### 3. 扩展性
- 新增消息类型时，前端只需要添加对应的UI处理逻辑
- 后端可以灵活控制显示内容

## 注意事项

### 1. 兼容性处理
对于可能缺失的字段，使用了安全的访问方式：
```javascript
text: data.step?.description || data.message || '执行步骤中...'
```

### 2. 临时实现标记
对于尚未完全实现SSE的功能（如语音处理），添加了注释说明：
```javascript
// 显示加载状态 - 注意：这里是临时的模拟实现
// 实际应该调用后端语音处理接口，并通过SSE接收状态更新
```

### 3. 数据结构适配
根据后端实际推送的数据结构，调整了前端的数据访问路径：
- `data.plan.totalSteps` - 执行计划的总步骤数
- `data.step.description` - 步骤描述信息
- `data.result.errCode` - 步骤执行结果状态

## 后续建议

1. **后端完善消息内容**：确保所有SSE消息都包含合适的 `message` 字段
2. **统一消息格式**：建议后端统一使用标准的消息格式
3. **完善错误处理**：为所有可能的错误情况提供友好的提示信息
4. **测试验证**：测试各种场景下的消息显示是否符合预期

# AI 助手消息显示逻辑前后端一体化重新设计方案

## 项目背景

完全重新设计 AI 助手的消息显示逻辑，包括前端消息处理和后端 SSE 消息推送机制。本方案不考虑向后兼容性，旨在实现最优的用户体验和最清晰的代码架构。

## 设计目标

### 核心用户体验流程

**1. 用户发送消息阶段**

- 用户输入并发送消息
- 立即在消息列表中显示用户消息
- 同时显示 AI 加载气泡，初始状态显示"AI 思考中..."

**2. AI 处理阶段**

- AI 开始处理时，加载气泡显示"分析意图中..."
- 意图识别完成后，根据意图类型更新加载气泡状态

**3. 分支处理逻辑**

**情况 A - 闲聊类型（chat）**

- 意图识别为 chat 后，立即隐藏加载气泡
- 在消息列表中创建新的 AI 消息气泡
- 开始流式显示 AI 回复内容（逐字显示效果）
- 流式显示完成后，消息状态变为最终状态

**情况 B - 任务类型（task）**

- 意图识别为 task 后，加载气泡继续显示
- 根据任务执行的不同阶段更新加载气泡文案：
  - "准备执行任务..."
  - "执行步骤：[具体步骤描述]"
  - "继续执行任务..."
- 任务执行完成后，隐藏加载气泡
- 在消息列表中添加一条格式化的任务完成消息

## 前后端一体化设计

#### 1.1 消息类型定义

```javascript
const SSE_MESSAGE_TYPES = {
  // 基础流程消息
  PROCESSING_START: 'processing_start', // 开始处理用户请求
  INTENT_ANALYZING: 'intent_analyzing', // 正在分析用户意图
  INTENT_RECOGNIZED: 'intent_recognized', // 意图识别完成

  // 聊天类型消息
  CHAT_RESPONSE_START: 'chat_response_start', // 开始生成聊天回复
  CHAT_CONTENT_CHUNK: 'chat_content_chunk', // 聊天内容流式块
  CHAT_RESPONSE_END: 'chat_response_end', // 聊天回复完成

  // 任务类型消息
  TASK_PREPARATION: 'task_preparation', // 任务准备阶段
  TASK_STEP_START: 'task_step_start', // 任务步骤开始
  TASK_STEP_PROGRESS: 'task_step_progress', // 任务步骤进度
  TASK_STEP_COMPLETE: 'task_step_complete', // 任务步骤完成
  TASK_ALL_COMPLETE: 'task_all_complete', // 所有任务完成

  // 错误和结束消息
  ERROR: 'error', // 处理错误
  SESSION_END: 'session_end', // 会话结束
}
```

#### 1.2 统一消息数据结构

```javascript
// 基础消息结构
interface BaseMessage {
  type: string; // 消息类型
  timestamp: number; // 时间戳
  sessionId: string; // 会话 ID
  data?: any; // 消息数据
}

// 意图识别消息
interface IntentMessage extends BaseMessage {
  type: 'intent_recognized';
  data: {
    intentType: 'chat' | 'task',
    confidence: number, // 识别置信度
    description: string, // 意图描述
  };
}

// 聊天内容消息
interface ChatContentMessage extends BaseMessage {
  type: 'chat_content_chunk';
  data: {
    content: string, // 内容片段
    isComplete: boolean, // 是否完成
    totalLength?: number, // 预期总长度
  };
}

// 任务步骤消息
interface TaskStepMessage extends BaseMessage {
  type: 'task_step_start' | 'task_step_progress' | 'task_step_complete';
  data: {
    stepId: string, // 步骤 ID
    stepName: string, // 步骤名称
    stepIndex: number, // 当前步骤索引
    totalSteps: number, // 总步骤数
    progress?: number, // 进度百分比
    result?: any, // 步骤结果
  };
}
```

#### 2.1 消息推送时机规划

```javascript
// 后端推送流程设计 (uniCloud-aliyun/cloudfunctions/ai/index.obj.js)
async function chatStreamSSE({ channel, message, messages: historyRecords }) {
  const sessionId = generateSessionId()

  try {
    // 1. 开始处理通知
    await channel.write({
      type: SSE_MESSAGE_TYPES.PROCESSING_START,
      timestamp: Date.now(),
      sessionId,
      data: { message: '开始处理您的请求...' },
    })

    // 2. 意图分析通知
    await channel.write({
      type: SSE_MESSAGE_TYPES.INTENT_ANALYZING,
      timestamp: Date.now(),
      sessionId,
      data: { message: '正在分析您的意图...' },
    })

    // 3. 意图识别结果
    const intentResult = await analyzeIntent(message)
    await channel.write({
      type: SSE_MESSAGE_TYPES.INTENT_RECOGNIZED,
      timestamp: Date.now(),
      sessionId,
      data: {
        intentType: intentResult.type,
        confidence: intentResult.confidence,
        description: intentResult.description,
      },
    })

    // 4. 根据意图类型分支处理
    if (intentResult.type === 'chat') {
      await handleChatFlow(channel, sessionId, message, historyRecords)
    } else if (intentResult.type === 'task') {
      await handleTaskFlow(channel, sessionId, message, historyRecords)
    }
  } catch (error) {
    await channel.write({
      type: SSE_MESSAGE_TYPES.ERROR,
      timestamp: Date.now(),
      sessionId,
      data: { error: error.message },
    })
  } finally {
    await channel.end({
      type: SSE_MESSAGE_TYPES.SESSION_END,
      timestamp: Date.now(),
      sessionId,
    })
  }
}
```

#### 2.2 聊天类型处理流程

```javascript
async function handleChatFlow(channel, sessionId, message, historyRecords) {
  // 1. 开始生成回复
  await channel.write({
    type: SSE_MESSAGE_TYPES.CHAT_RESPONSE_START,
    timestamp: Date.now(),
    sessionId,
    data: { message: '正在生成回复...' },
  })

  // 2. 调用 AI 生成回复并流式推送
  const aiResponse = await generateChatResponse(message, historyRecords)

  for await (const chunk of aiResponse) {
    await channel.write({
      type: SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK,
      timestamp: Date.now(),
      sessionId,
      data: {
        content: chunk.content,
        isComplete: chunk.isComplete,
        totalLength: chunk.totalLength,
      },
    })
  }

  // 3. 回复完成
  await channel.write({
    type: SSE_MESSAGE_TYPES.CHAT_RESPONSE_END,
    timestamp: Date.now(),
    sessionId,
    data: { message: '回复完成' },
  })
}
```

#### 2.3 任务类型处理流程

```javascript
async function handleTaskFlow(channel, sessionId, message, historyRecords) {
  // 1. 任务准备阶段
  await channel.write({
    type: SSE_MESSAGE_TYPES.TASK_PREPARATION,
    timestamp: Date.now(),
    sessionId,
    data: { message: '正在准备执行任务...' },
  })

  // 2. 生成任务执行计划
  const executionPlan = await generateExecutionPlan(message)

  // 3. 执行任务步骤
  for (let i = 0; i < executionPlan.steps.length; i++) {
    const step = executionPlan.steps[i]

    // 步骤开始
    await channel.write({
      type: SSE_MESSAGE_TYPES.TASK_STEP_START,
      timestamp: Date.now(),
      sessionId,
      data: {
        stepId: step.id,
        stepName: step.name,
        stepIndex: i + 1,
        totalSteps: executionPlan.steps.length,
      },
    })

    // 执行步骤并推送进度
    const stepResult = await executeTaskStep(step, (progress) => {
      channel.write({
        type: SSE_MESSAGE_TYPES.TASK_STEP_PROGRESS,
        timestamp: Date.now(),
        sessionId,
        data: {
          stepId: step.id,
          stepName: step.name,
          stepIndex: i + 1,
          totalSteps: executionPlan.steps.length,
          progress: progress,
        },
      })
    })

    // 步骤完成
    await channel.write({
      type: SSE_MESSAGE_TYPES.TASK_STEP_COMPLETE,
      timestamp: Date.now(),
      sessionId,
      data: {
        stepId: step.id,
        stepName: step.name,
        stepIndex: i + 1,
        totalSteps: executionPlan.steps.length,
        result: stepResult,
      },
    })
  }

  // 4. 所有任务完成
  await channel.write({
    type: SSE_MESSAGE_TYPES.TASK_ALL_COMPLETE,
    timestamp: Date.now(),
    sessionId,
    data: {
      message: '所有任务执行完成',
      summary: generateTaskSummary(executionPlan),
    },
  })
}
```

### 3. 前端消息处理逻辑重新设计

#### 3.1 统一状态管理

```javascript
// 前端状态管理 (src/pages/aiAssistant/index.vue)
const aiState = ref({
  // 会话状态
  sessionId: null,
  isProcessing: false,

  // 加载状态
  loading: {
    show: false,
    text: '',
    stage: '', // thinking/analyzing/preparing/executing/completing
    progress: null,
  },

  // 流式消息状态
  streaming: {
    active: false,
    messageId: null,
    intentType: null,
  },
})

// 状态控制函数
const showLoading = (text, stage = 'thinking') => {
  aiState.value.loading = {
    show: true,
    text,
    stage,
    progress: null,
  }
}

const updateLoading = (updates) => {
  if (aiState.value.loading.show) {
    Object.assign(aiState.value.loading, updates)
  }
}

const hideLoading = () => {
  aiState.value.loading.show = false
}
```

#### 3.2 消息类型定义

```javascript
const MESSAGE_TYPES = {
  USER: 'user', // 用户消息
  AI_STREAMING: 'ai_streaming', // AI 流式消息
  AI_COMPLETE: 'ai_complete', // AI 完成消息
  TASK_COMPLETE: 'task_complete', // 任务完成消息
  ERROR: 'error', // 错误消息
}

const MESSAGE_STATUS = {
  SENDING: 'sending', // 发送中
  STREAMING: 'streaming', // 流式显示中
  COMPLETE: 'complete', // 完成
  ERROR: 'error', // 错误
}
```

#### 3.3 全新消息处理逻辑

```javascript
const handleStreamMessage = (message) => {
  const { type, data, sessionId, timestamp } = message

  // 验证会话 ID
  if (aiState.value.sessionId && sessionId !== aiState.value.sessionId) {
    console.warn('收到不匹配的会话消息，忽略')
    return
  }

  switch (type) {
    case SSE_MESSAGE_TYPES.PROCESSING_START:
      aiState.value.sessionId = sessionId
      aiState.value.isProcessing = true
      showLoading('AI 思考中...', 'thinking')
      break

    case SSE_MESSAGE_TYPES.INTENT_ANALYZING:
      updateLoading({
        text: '分析意图中...',
        stage: 'analyzing',
      })
      break

    case SSE_MESSAGE_TYPES.INTENT_RECOGNIZED:
      aiState.value.streaming.intentType = data.intentType

      if (data.intentType === 'chat') {
        updateLoading({
          text: '准备回复中...',
          stage: 'preparing',
        })
      } else if (data.intentType === 'task') {
        updateLoading({
          text: '准备执行任务...',
          stage: 'executing',
        })
      }
      break

    case SSE_MESSAGE_TYPES.CHAT_RESPONSE_START:
      // 聊天开始，准备流式显示
      updateLoading({
        text: '正在生成回复...',
        stage: 'generating',
      })
      break

    case SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK:
      // 首次收到内容时，切换到流式显示
      if (!aiState.value.streaming.active) {
        hideLoading()
        createStreamingMessage()
      }
      appendStreamingContent(data.content)
      break

    case SSE_MESSAGE_TYPES.CHAT_RESPONSE_END:
      // 聊天回复完成
      finalizeStreamingMessage()
      resetAiState()
      break

    case SSE_MESSAGE_TYPES.TASK_PREPARATION:
      updateLoading({
        text: '正在准备执行任务...',
        stage: 'preparing',
      })
      break

    case SSE_MESSAGE_TYPES.TASK_STEP_START:
      updateLoading({
        text: `执行步骤：${data.stepName}`,
        stage: 'executing',
        progress: {
          current: data.stepIndex,
          total: data.totalSteps,
        },
      })
      break

    case SSE_MESSAGE_TYPES.TASK_STEP_PROGRESS:
      updateLoading({
        progress: {
          current: data.stepIndex,
          total: data.totalSteps,
          percent: data.progress,
        },
      })
      break

    case SSE_MESSAGE_TYPES.TASK_STEP_COMPLETE:
      // 步骤完成，继续显示加载状态等待下一步骤
      break

    case SSE_MESSAGE_TYPES.TASK_ALL_COMPLETE:
      hideLoading()
      addTaskCompleteMessage(data)
      resetAiState()
      break

    case SSE_MESSAGE_TYPES.ERROR:
      hideLoading()
      addErrorMessage(data.error)
      resetAiState()
      break

    case SSE_MESSAGE_TYPES.SESSION_END:
      resetAiState()
      break
  }
}
```

#### 3.4 消息操作函数

```javascript
const createStreamingMessage = () => {
  const streamingMessage = {
    _id: `streaming_${Date.now()}`,
    type: MESSAGE_TYPES.AI_STREAMING,
    content: '',
    isUser: false,
    status: MESSAGE_STATUS.STREAMING,
    time: new Date().toISOString(),
  }
  messages.value.push(streamingMessage)
  aiState.value.streaming.active = true
  aiState.value.streaming.messageId = streamingMessage._id
}

const appendStreamingContent = (content) => {
  if (aiState.value.streaming.messageId) {
    const messageIndex = messages.value.findIndex((msg) => msg._id === aiState.value.streaming.messageId)
    if (messageIndex !== -1) {
      messages.value[messageIndex].content += content
    }
  }
}

const finalizeStreamingMessage = () => {
  if (aiState.value.streaming.messageId) {
    const messageIndex = messages.value.findIndex((msg) => msg._id === aiState.value.streaming.messageId)
    if (messageIndex !== -1) {
      messages.value[messageIndex].status = MESSAGE_STATUS.COMPLETE
      messages.value[messageIndex].type = MESSAGE_TYPES.AI_COMPLETE
    }
  }
}

const addTaskCompleteMessage = (data) => {
  const taskMessage = {
    _id: `task_${Date.now()}`,
    type: MESSAGE_TYPES.TASK_COMPLETE,
    content: data.message,
    summary: data.summary,
    isUser: false,
    status: MESSAGE_STATUS.COMPLETE,
    time: new Date().toISOString(),
  }
  messages.value.push(taskMessage)
}

const addErrorMessage = (error) => {
  const errorMessage = {
    _id: `error_${Date.now()}`,
    type: MESSAGE_TYPES.ERROR,
    content: `抱歉，处理过程中出现错误：${error}`,
    isUser: false,
    status: MESSAGE_STATUS.ERROR,
    time: new Date().toISOString(),
  }
  messages.value.push(errorMessage)
}

const resetAiState = () => {
  aiState.value = {
    sessionId: null,
    isProcessing: false,
    loading: {
      show: false,
      text: '',
      stage: '',
      progress: null,
    },
    streaming: {
      active: false,
      messageId: null,
      intentType: null,
    },
  }
}
```

### 4. UI 组件重新设计

#### 4.1 加载气泡组件重构

```vue
<!-- src/pages/aiAssistant/components/l-loading-bubble.vue -->
<template>
  <div v-if="loading.show" class="loading-bubble" :class="`stage-${loading.stage}`">
    <div class="loading-content">
      <!-- 加载动画 -->
      <div class="loading-dots">
        <div v-for="i in 3" :key="i" class="dot" :style="{ animationDelay: `${(i - 1) * 0.15}s` }"></div>
      </div>

      <!-- 加载文案 -->
      <div class="loading-text">{{ loading.text }}</div>

      <!-- 任务进度信息 -->
      <div v-if="loading.progress" class="progress-info">
        <span class="step-info"> 步骤 {{ loading.progress.current }}/{{ loading.progress.total }} </span>
        <div v-if="loading.progress.percent" class="progress-bar">
          <div class="progress-fill" :style="{ width: `${loading.progress.percent}%` }"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  loading: {
    type: Object,
    required: true,
  },
})
</script>
```

#### 4.2 样式优化

```scss
<style lang="scss" scoped>
.loading-bubble {
  max-width: 70%;
  border-radius: 12px;
  padding: 12px 16px;
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  margin-bottom: 12px;

  &.stage-thinking {
    border-left: 3px solid var(--color-primary);
  }

  &.stage-analyzing {
    border-left: 3px solid var(--color-warning);
  }

  &.stage-executing {
    border-left: 3px solid var(--color-success);
  }

  &.stage-generating {
    border-left: 3px solid var(--color-info);
  }

  .loading-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
  }

  .loading-dots {
    display: flex;
    gap: 4px;

    .dot {
      width: 8px;
      height: 8px;
      background-color: var(--color-gray-400);
      border-radius: 50%;
      animation: loading-pulse 1.4s ease-in-out infinite;
    }
  }

  .loading-text {
    font-size: 14px;
    color: var(--color-gray-600);
    flex: 1;
  }

  .progress-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 120px;

    .step-info {
      font-size: 12px;
      color: var(--color-gray-500);
      text-align: right;
    }

    .progress-bar {
      height: 4px;
      background-color: var(--color-gray-200);
      border-radius: 2px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background-color: var(--color-success);
        transition: width 0.3s ease;
      }
    }
  }
}

@keyframes loading-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.15);
    opacity: 1;
  }
}
</style>
```

### 5. 发送消息流程重构

#### 5.1 统一发送入口

```javascript
// src/pages/aiAssistant/index.vue
const handleSendMessage = async () => {
  if (!inputValue.value.trim() || aiState.value.isProcessing) return

  // 1. 添加用户消息
  addUserMessage(inputValue.value)

  // 2. 清空输入框
  const userMessage = inputValue.value
  inputValue.value = ''

  // 3. 滚动到底部
  nextTick(() => {
    messageListRef.value?.scrollToBottom()
  })

  // 4. 发送请求
  await sendMessageToAI(userMessage)
}

const addUserMessage = (content) => {
  const userMessage = {
    _id: `user_${Date.now()}`,
    type: MESSAGE_TYPES.USER,
    content,
    isUser: true,
    status: MESSAGE_STATUS.COMPLETE,
    time: new Date().toISOString(),
  }
  messages.value.push(userMessage)
}

const sendMessageToAI = async (userMessage) => {
  try {
    const channel = new uniCloud.SSEChannel()

    channel.on('open', () => {
      console.log('SSE 连接已建立')
    })

    channel.on('message', (data) => {
      handleStreamMessage(data)
    })

    channel.on('end', (data) => {
      if (data) {
        handleStreamMessage(data)
      }
    })

    channel.on('error', (error) => {
      console.error('SSE 连接错误：', error)
      handleStreamMessage({
        type: SSE_MESSAGE_TYPES.ERROR,
        data: { error: error.message || '连接错误' },
      })
    })

    await channel.open()

    const historyMessages = messages.value
      .filter((msg) => msg.type === MESSAGE_TYPES.USER || msg.type === MESSAGE_TYPES.AI_COMPLETE)
      .map((msg) => ({
        role: msg.isUser ? 'user' : 'assistant',
        content: msg.content,
      }))

    const response = await aiApi.chatStreamSSE({
      message: userMessage,
      messages: historyMessages,
      channel: channel,
    })

    if (response.errCode !== 0) {
      throw new Error(response.errMsg || '调用 AI 接口失败')
    }
  } catch (error) {
    console.error('发送消息失败：', error)
    handleStreamMessage({
      type: SSE_MESSAGE_TYPES.ERROR,
      data: { error: error.message },
    })
  }
}
```

## 实施计划

### 阶段一：后端 SSE 消息协议重构（2-3 天）

1. 重新定义 SSE 消息类型和数据结构
2. 重构后端推送逻辑和时机
3. 实现聊天和任务的分支处理流程
4. 完善错误处理和会话管理

### 阶段二：前端状态管理重构（2-3 天）

1. 重构前端状态管理机制
2. 实现新的消息处理逻辑
3. 重新设计 UI 组件和交互
4. 优化加载状态显示

### 阶段三：集成测试和优化（2 天）

1. 前后端联调测试
2. 用户体验测试和优化
3. 性能测试和优化
4. 边界情况处理

### 阶段四：文档和部署（1 天）

1. 编写技术文档
2. 更新 API 文档
3. 部署和验证
4. 用户培训

## 预期效果

### 用户体验提升

1. **统一的交互体验**：聊天和任务类型有清晰区分但体验一致
2. **实时状态反馈**：用户始终了解 AI 当前的处理状态
3. **流畅的界面切换**：加载状态与消息显示无缝切换
4. **详细的进度显示**：任务执行过程中显示具体步骤和进度

### 技术架构优化

1. **清晰的消息协议**：前后端消息格式统一规范
2. **可维护的代码结构**：状态管理集中，逻辑清晰
3. **完善的错误处理**：各种异常情况都有合适处理
4. **高性能的实现**：避免不必要的状态更新和重渲染

## 总结

本方案完全重新设计了 AI 助手的消息显示逻辑，包括前后端的完整重构。通过统一的 SSE 消息协议、清晰的状态管理、优化的 UI 组件和流畅的用户体验，实现了规范化的 AI 助手交互系统。方案不考虑向后兼容性，专注于最优的技术架构和用户体验。

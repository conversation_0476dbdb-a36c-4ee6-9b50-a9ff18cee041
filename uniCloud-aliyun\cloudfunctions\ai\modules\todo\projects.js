/**
 * Todo 项目管理模块
 * 负责项目的 CRUD 操作和查询功能
 */

const { API_CONFIG, ERROR_CODES } = require('./config')
const {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  simplifyProjectData,
  removeEmptyFields,
} = require('./utils')

/**
 * 项目管理类
 */
class ProjectManager {
  constructor(authManager) {
    this.authManager = authManager
    console.log('[ProjectManager] 项目管理器初始化完成')
  }

  /**
   * 获取项目列表
   * @param {object} options - 查询参数
   * @returns {object} 项目列表
   */
  async getProjects(options = {}) {
    const { keyword = null, includeClosed = false } = options
    console.log('[ProjectManager] [getProjects] 开始获取项目列表，过滤条件：', {
      keyword,
      includeClosed,
    })

    try {
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[ProjectManager] [getProjects] 获取基础数据失败：', batchResult)
        return batchResult
      }

      let { projects } = batchResult.data
      let filteredProjects = []
      console.log(`[ProjectManager] [getProjects] 原始项目数：${projects.length}`)

      for (const project of projects) {
        // 是否包含已关闭的项目
        if (!includeClosed && project.closed) continue

        // 关键词筛选
        if (keyword) {
          const searchText = `${project.name || ''}`.toLowerCase()
          if (!searchText.includes(keyword.toLowerCase())) continue
        }

        // 简化项目数据
        const simplifiedProject = simplifyProjectData(project)
        filteredProjects.push(simplifiedProject)
      }

      console.log(`[ProjectManager] [getProjects] 筛选后项目数：${filteredProjects.length}`)
      return createSuccessResponse('获取项目列表成功', filteredProjects)
    } catch (error) {
      console.error('[ProjectManager] [getProjects] 获取项目列表异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取项目列表失败', error)
    }
  }

  /**
   * 创建项目
   * @param {object} options - 项目数据
   * @returns {object} 创建结果
   */
  async createProject(options = {}) {
    // 使用安全的参数处理方式，类似 Python 版本
    const { name = null, color = null, kind = null } = options
    console.log(`[ProjectManager] [createProject] 开始创建项目，名称：${name}`)

    // 参数校验
    const validation = validateParams({ name }, ['name'])
    if (validation) {
      console.warn('[ProjectManager] [createProject] 参数校验失败：', validation)
      return validation
    }

    try {
      // 使用安全的参数处理方式，类似 Python 版本
      const safeProjectData = {
        name: name,
        color: color !== null ? color : '#3498db',
        kind: kind !== null ? kind : 'TASK',
        closed: false,
      }

      // 移除空值字段
      const cleanProjectData = removeEmptyFields(safeProjectData)
      console.log('[ProjectManager] [createProject] 准备发送的项目数据：', cleanProjectData)

      // 发送创建请求
      const result = await this.authManager._request('POST', API_CONFIG.PROJECT_URL, cleanProjectData)
      if (result.errCode) {
        console.error('[ProjectManager] [createProject] 创建项目失败：', result)
        return result
      }

      console.log('[ProjectManager] [createProject] 项目创建成功')
      return createSuccessResponse('项目创建成功', result.data)
    } catch (error) {
      console.error('[ProjectManager] [createProject] 创建项目异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '创建项目失败', error)
    }
  }

  /**
   * 获取单个项目详情
   * @param {string} projectId - 项目 ID
   * @returns {object} 项目详情
   */
  async getProject(projectId) {
    console.log(`[ProjectManager] [getProject] 开始获取项目详情，ID: ${projectId}`)

    // 参数校验
    const validation = validateParams({ projectId }, ['projectId'])
    if (validation) {
      console.warn('[ProjectManager] [getProject] 参数校验失败：', validation)
      return validation
    }

    try {
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[ProjectManager] [getProject] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { projects } = batchResult.data
      const project = projects.find((p) => p.id === projectId)

      if (!project) {
        console.warn(`[ProjectManager] [getProject] 未找到项目：${projectId}`)
        return createErrorResponse(ERROR_CODES.PROJECT_NOT_FOUND, '项目不存在')
      }

      // 简化项目数据
      const simplifiedProject = simplifyProjectData(project)

      console.log('[ProjectManager] [getProject] 获取项目详情成功')
      return createSuccessResponse('获取项目详情成功', simplifiedProject)
    } catch (error) {
      console.error('[ProjectManager] [getProject] 获取项目详情异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取项目详情失败', error)
    }
  }

  /**
   * 更新项目
   * @param {string} projectId - 项目 ID
   * @param {object} updateData - 更新数据
   * @returns {object} 更新结果
   */
  async updateProject(projectId, updateData) {
    console.log(`[ProjectManager] [updateProject] 开始更新项目，ID: ${projectId}`, {
      updateData,
    })

    // 参数校验
    const validation = validateParams({ projectId }, ['projectId'])
    if (validation) {
      console.warn('[ProjectManager] [updateProject] 参数校验失败：', validation)
      return validation
    }

    try {
      // 首先获取现有项目数据
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[ProjectManager] [updateProject] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { projects } = batchResult.data

      // 查找现有项目
      const existingProject = projects.find((p) => p.id === projectId)
      if (!existingProject) {
        console.error(`[ProjectManager] [updateProject] 未找到项目：${projectId}`)
        return createErrorResponse(ERROR_CODES.PROJECT_NOT_FOUND, `未找到项目：${projectId}`)
      }

      // 使用安全的参数处理方式，类似 Python 版本
      const safeUpdateData = {
        id: projectId,
        name: updateData.name !== undefined ? updateData.name : existingProject.name,
        color: updateData.color !== undefined ? updateData.color : existingProject.color,
        kind: updateData.kind !== undefined ? updateData.kind : existingProject.kind,
        closed: updateData.closed !== undefined ? updateData.closed : existingProject.closed,
      }

      // 移除空值字段
      const cleanUpdateData = removeEmptyFields(safeUpdateData)
      console.log('[ProjectManager] [updateProject] 准备发送的更新数据：', cleanUpdateData)

      // 发送更新请求
      const result = await this.authManager._request('POST', `${API_CONFIG.PROJECT_URL}/${projectId}`, cleanUpdateData)
      if (result.errCode) {
        console.error('[ProjectManager] [updateProject] 更新项目失败：', result)
        return result
      }

      console.log('[ProjectManager] [updateProject] 项目更新成功')
      return createSuccessResponse('项目更新成功', result.data)
    } catch (error) {
      console.error('[ProjectManager] [updateProject] 更新项目异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '更新项目失败', error)
    }
  }

  /**
   * 删除项目
   * @param {string} projectId - 项目 ID
   * @returns {object} 删除结果
   */
  async deleteProject(projectId) {
    console.log(`[ProjectManager] [deleteProject] 开始删除项目，ID: ${projectId}`)

    // 参数校验
    const validation = validateParams({ projectId }, ['projectId'])
    if (validation) {
      console.warn('[ProjectManager] [deleteProject] 参数校验失败：', validation)
      return validation
    }

    try {
      // 发送删除请求
      const result = await this.authManager._request('DELETE', `${API_CONFIG.PROJECT_URL}/${projectId}`)
      if (result.errCode) {
        console.error('[ProjectManager] [deleteProject] 删除项目失败：', result)
        return result
      }

      console.log('[ProjectManager] [deleteProject] 项目删除成功')
      return createSuccessResponse('项目删除成功', result.data)
    } catch (error) {
      console.error('[ProjectManager] [deleteProject] 删除项目异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '删除项目失败', error)
    }
  }
}

module.exports = ProjectManager
